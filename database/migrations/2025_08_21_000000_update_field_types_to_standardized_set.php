<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update existing field type data to match new standardized types
        $this->migrateExistingFieldTypes();
        
        // Since SQLite doesn't support modifying ENUM columns directly,
        // we need to recreate the table with the new ENUM values
        $this->updateFieldTypeEnum();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert field types back to original values
        $this->revertFieldTypes();
        
        // Recreate the table with original ENUM values
        $this->revertFieldTypeEnum();
    }

    /**
     * Migrate existing field type data to new standardized types
     */
    private function migrateExistingFieldTypes(): void
    {
        // Map old field types to new standardized types
        $typeMapping = [
            'Soccer' => 'Soccer',
            'Basketball' => 'Multi-Purpose', // Basketball becomes Multi-Purpose
            'Tennis' => 'Multi-Purpose',     // Tennis becomes Multi-Purpose
            'Volleyball' => 'Multi-Purpose', // Volleyball becomes Multi-Purpose
            'Baseball' => 'Multi-Purpose',   // Baseball becomes Multi-Purpose
            'Football' => 'Soccer',          // Football becomes Soccer
            'Multi-Purpose' => 'Multi-Purpose', // Stays the same
        ];

        // Special handling for FPMP fields based on field names
        $specialMappings = [
            'Veld Futbol' => 'Soccer',
            'Veld Multi' => 'Multi-Purpose',
            'Veld Bolas' => 'Bolas',
            'Patio Area' => 'Patio',
        ];

        // Update special FPMP fields first
        foreach ($specialMappings as $fieldName => $newType) {
            DB::table('fields')
                ->where('name', $fieldName)
                ->update(['type' => $newType]);
        }

        // Update remaining fields based on type mapping
        foreach ($typeMapping as $oldType => $newType) {
            DB::table('fields')
                ->where('type', $oldType)
                ->whereNotIn('name', array_keys($specialMappings))
                ->update(['type' => $newType]);
        }

        // Handle any fields that might need to become Sportspark or Office
        // (These would need to be identified based on specific business logic)
        // For now, we'll leave this as a manual process or future enhancement
    }

    /**
     * Update the ENUM column to use new field types
     */
    private function updateFieldTypeEnum(): void
    {
        // For SQLite, we need to recreate the table
        if (DB::getDriverName() === 'sqlite') {
            $this->recreateTableForSQLite();
        } else {
            // For MySQL/PostgreSQL, we can modify the ENUM directly
            DB::statement("ALTER TABLE fields MODIFY COLUMN type ENUM('Sportspark', 'Patio', 'Office', 'Multi-Purpose', 'Soccer', 'Bolas')");
        }
    }

    /**
     * Recreate the fields table for SQLite with new ENUM values
     */
    private function recreateTableForSQLite(): void
    {
        // Drop temp table if it exists from previous failed migration
        Schema::dropIfExists('fields_temp');

        // Create temporary table with new structure (matching current fields table exactly)
        Schema::create('fields_temp', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['Sportspark', 'Patio', 'Office', 'Multi-Purpose', 'Soccer', 'Bolas']);
            $table->text('description')->nullable();
            $table->decimal('hourly_rate', 8, 2);
            $table->decimal('night_hourly_rate', 8, 2)->nullable();
            $table->time('night_time_start')->nullable();
            $table->integer('capacity');
            $table->enum('status', ['Active', 'Inactive', 'Under Maintenance'])->default('Active');
            $table->time('opening_time')->default('08:00');
            $table->time('closing_time')->default('22:00');
            $table->decimal('min_booking_hours', 4, 1)->default(0.5);
            $table->decimal('max_booking_hours', 4, 1)->default(8.0);
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Indexes for performance
            $table->index(['status']);
            $table->index(['type']);
        });

        // Copy data from original table to temporary table with explicit column mapping
        DB::statement('INSERT INTO fields_temp (id, name, type, description, hourly_rate, night_hourly_rate, night_time_start, capacity, status, opening_time, closing_time, min_booking_hours, max_booking_hours, start_date, end_date, deleted_at, created_at, updated_at)
                       SELECT id, name, type, description, hourly_rate, night_hourly_rate, night_time_start, capacity, status, opening_time, closing_time, min_booking_hours, max_booking_hours, start_date, end_date, deleted_at, created_at, updated_at FROM fields');

        // Drop original table
        Schema::drop('fields');

        // Rename temporary table to original name
        Schema::rename('fields_temp', 'fields');
    }

    /**
     * Revert field types back to original values
     */
    private function revertFieldTypes(): void
    {
        // Map new field types back to original types
        $revertMapping = [
            'Soccer' => 'Soccer',
            'Multi-Purpose' => 'Multi-Purpose',
            'Bolas' => 'Multi-Purpose',
            'Patio' => 'Multi-Purpose',
            'Sportspark' => 'Multi-Purpose',
            'Office' => 'Multi-Purpose',
        ];

        foreach ($revertMapping as $newType => $oldType) {
            DB::table('fields')
                ->where('type', $newType)
                ->update(['type' => $oldType]);
        }
    }

    /**
     * Revert the ENUM column to original field types
     */
    private function revertFieldTypeEnum(): void
    {
        if (DB::getDriverName() === 'sqlite') {
            $this->revertTableForSQLite();
        } else {
            DB::statement("ALTER TABLE fields MODIFY COLUMN type ENUM('Soccer', 'Basketball', 'Tennis', 'Volleyball', 'Baseball', 'Football', 'Multi-Purpose')");
        }
    }

    /**
     * Revert the fields table for SQLite with original ENUM values
     */
    private function revertTableForSQLite(): void
    {
        // Create temporary table with original structure
        Schema::create('fields_temp', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['Soccer', 'Basketball', 'Tennis', 'Volleyball', 'Baseball', 'Football', 'Multi-Purpose']);
            $table->text('description')->nullable();
            $table->decimal('hourly_rate', 8, 2);
            $table->decimal('night_hourly_rate', 8, 2)->nullable();
            $table->time('night_time_start')->nullable();
            $table->integer('capacity');
            $table->enum('status', ['Active', 'Inactive', 'Under Maintenance'])->default('Active');
            $table->time('opening_time')->default('08:00');
            $table->time('closing_time')->default('22:00');
            $table->decimal('min_booking_hours', 4, 1)->default(0.5);
            $table->decimal('max_booking_hours', 4, 1)->default(8.0);
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->softDeletes();
            $table->timestamps();

            // Indexes for performance
            $table->index(['status']);
            $table->index(['type']);
        });

        // Copy data from current table to temporary table with explicit column mapping
        DB::statement('INSERT INTO fields_temp (id, name, type, description, hourly_rate, night_hourly_rate, night_time_start, capacity, status, opening_time, closing_time, min_booking_hours, max_booking_hours, start_date, end_date, deleted_at, created_at, updated_at)
                       SELECT id, name, type, description, hourly_rate, night_hourly_rate, night_time_start, capacity, status, opening_time, closing_time, min_booking_hours, max_booking_hours, start_date, end_date, deleted_at, created_at, updated_at FROM fields');

        // Drop current table
        Schema::drop('fields');

        // Rename temporary table to original name
        Schema::rename('fields_temp', 'fields');
    }
};
