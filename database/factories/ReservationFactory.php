<?php

namespace Database\Factories;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Reservation>
 */
class ReservationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Reservation::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startTime = fake()->time('H:i', '20:00');
        $startCarbon = Carbon::createFromFormat('H:i', $startTime);
        $durationHours = fake()->numberBetween(1, 4);
        $endCarbon = $startCarbon->copy()->addHours($durationHours);
        $endTime = $endCarbon->format('H:i');

        $statuses = ['Pending', 'Confirmed', 'Cancelled', 'Completed'];
        $status = fake()->randomElement($statuses);

        return [
            'field_id' => Field::factory(),
            'user_id' => User::factory(),
            'booking_date' => fake()->dateTimeBetween('now', '+30 days')->format('Y-m-d'),
            'start_time' => $startTime,
            'end_time' => $endTime,
            'duration_hours' => $durationHours,
            'total_cost' => fake()->randomFloat(2, 50, 300),
            'status' => $status,
            'customer_name' => fake()->name(),
            'customer_email' => fake()->safeEmail(),
            'customer_phone' => fake()->phoneNumber(),
            'special_requests' => fake()->optional()->sentence(),
            'confirmed_at' => $status === 'Confirmed' ? fake()->dateTimeBetween('-7 days', 'now') : null,
            'cancelled_at' => $status === 'Cancelled' ? fake()->dateTimeBetween('-7 days', 'now') : null,
        ];
    }

    /**
     * Indicate that the reservation is confirmed.
     */
    public function confirmed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Confirmed',
            'confirmed_at' => fake()->dateTimeBetween('-7 days', 'now'),
            'cancelled_at' => null,
        ]);
    }

    /**
     * Indicate that the reservation is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Pending',
            'confirmed_at' => null,
            'cancelled_at' => null,
        ]);
    }

    /**
     * Indicate that the reservation is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Cancelled',
            'confirmed_at' => null,
            'cancelled_at' => fake()->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Indicate that the reservation is for today.
     */
    public function today(): static
    {
        return $this->state(fn (array $attributes) => [
            'booking_date' => now()->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the reservation is upcoming.
     */
    public function upcoming(): static
    {
        return $this->state(fn (array $attributes) => [
            'booking_date' => fake()->dateTimeBetween('tomorrow', '+30 days')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the reservation is for a specific field.
     */
    public function forField(Field $field): static
    {
        return $this->state(fn (array $attributes) => [
            'field_id' => $field->id,
            'total_cost' => $field->hourly_rate * $attributes['duration_hours'],
        ]);
    }

    /**
     * Indicate that the reservation is for a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'customer_name' => $user->name,
            'customer_email' => $user->email,
        ]);
    }

    /**
     * Create a reservation within working hours (8 AM - 10 PM).
     */
    public function withinWorkingHours(): static
    {
        return $this->state(function (array $attributes) {
            $startHour = fake()->numberBetween(8, 20); // 8 AM to 8 PM
            $startTime = sprintf('%02d:00', $startHour);
            $durationHours = fake()->numberBetween(1, min(4, 22 - $startHour)); // Don't exceed 10 PM
            $endTime = sprintf('%02d:00', $startHour + $durationHours);

            return [
                'start_time' => $startTime,
                'end_time' => $endTime,
                'duration_hours' => $durationHours,
            ];
        });
    }

    /**
     * Create a reservation for FPMP fields.
     */
    public function fpmp(): static
    {
        return $this->state(function (array $attributes) {
            // Get FPMP fields
            $fpmpFields = Field::whereIn('name', [
                'Veld Futbol',
                'Veld Multi',
                'Veld Bolas',
                'Patio Area',
            ])->get();

            if ($fpmpFields->isEmpty()) {
                // Fallback to any field
                $field = Field::first();
            } else {
                $field = $fpmpFields->random();
            }

            return [
                'field_id' => $field->id,
                'total_cost' => $field->hourly_rate * $attributes['duration_hours'],
            ];
        });
    }
}
