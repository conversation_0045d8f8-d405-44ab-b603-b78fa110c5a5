<?php

namespace Database\Factories;

use App\Models\Field;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Field>
 */
class FieldFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['Sportspark', 'Patio', 'Office', 'Multi-Purpose', 'Soccer', 'Bolas'];
        $statuses = ['Active', 'Inactive', 'Under Maintenance'];

        return [
            'name' => fake()->words(2, true).' Field',
            'type' => fake()->randomElement($types),
            'description' => fake()->sentence(10),
            'hourly_rate' => fake()->randomFloat(2, 25, 150),
            'capacity' => fake()->numberBetween(10, 100),
            'status' => fake()->randomElement($statuses),
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => fake()->randomFloat(1, 0.5, 2.0), // 0.5 to 2 hours
            'max_booking_hours' => fake()->randomFloat(1, 4.0, 8.0), // 4 to 8 hours
        ];
    }

    /**
     * Indicate that the field is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Active',
        ]);
    }

    /**
     * Indicate that the field is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Inactive',
        ]);
    }

    /**
     * Indicate that the field is under maintenance.
     */
    public function underMaintenance(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Under Maintenance',
        ]);
    }

    /**
     * Create a soccer field.
     */
    public function soccer(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'Soccer',
            'name' => 'Soccer Field '.fake()->randomLetter(),
            'capacity' => fake()->numberBetween(20, 50),
            'hourly_rate' => fake()->randomFloat(2, 50, 100),
        ]);
    }

    /**
     * Create a multi-purpose field.
     */
    public function multiPurpose(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'Multi-Purpose',
            'name' => 'Multi-Purpose Field '.fake()->numberBetween(1, 10),
            'capacity' => fake()->numberBetween(10, 30),
            'hourly_rate' => fake()->randomFloat(2, 30, 70),
        ]);
    }

    /**
     * Create a bolas field.
     */
    public function bolas(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'Bolas',
            'name' => 'Bolas Field '.fake()->randomLetter(),
            'capacity' => fake()->numberBetween(4, 16),
            'hourly_rate' => fake()->randomFloat(2, 15, 30),
        ]);
    }

    /**
     * Create a patio area.
     */
    public function patio(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'Patio',
            'name' => 'Patio Area '.fake()->randomLetter(),
            'capacity' => fake()->numberBetween(20, 60),
            'hourly_rate' => fake()->randomFloat(2, 25, 50),
        ]);
    }

    /**
     * Create a sportspark field.
     */
    public function sportspark(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'Sportspark',
            'name' => 'Sportspark Field '.fake()->randomLetter(),
            'capacity' => fake()->numberBetween(50, 100),
            'hourly_rate' => fake()->randomFloat(2, 75, 150),
        ]);
    }

    /**
     * Create an office space.
     */
    public function office(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'Office',
            'name' => 'Office Space '.fake()->randomLetter(),
            'capacity' => fake()->numberBetween(5, 20),
            'hourly_rate' => fake()->randomFloat(2, 40, 80),
        ]);
    }

    // Legacy methods for backward compatibility - redirect to new types
    /**
     * @deprecated Use multiPurpose() instead
     */
    public function basketball(): static
    {
        return $this->multiPurpose();
    }

    /**
     * @deprecated Use bolas() instead
     */
    public function tennis(): static
    {
        return $this->bolas();
    }
}
