<?php

namespace Tests\Unit;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Models\Reservation::class)]
class ReservationModelTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->field = Field::factory()->create([
            'hourly_rate' => 50.00,
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1,
            'max_booking_hours' => 8,
        ]);
    }

    #[Test]
    public function it_calculates_total_cost_correctly()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '13:00',
            'duration_hours' => 3,
        ]);

        $expectedCost = $this->field->hourly_rate * 3;
        $this->assertEquals($expectedCost, $reservation->calculateTotalCost());
    }

    #[Test]
    public function it_formats_time_range_correctly()
    {
        $reservation = Reservation::factory()->create([
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);

        $this->assertEquals('10:00 - 12:00', $reservation->time_range);
    }

    #[Test]
    public function it_formats_date_time_correctly()
    {
        $date = Carbon::parse('2024-06-25');
        $reservation = Reservation::factory()->create([
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);

        $expected = $date->format('M d, Y').' at 10:00 - 12:00';
        $this->assertEquals($expected, $reservation->formatted_date_time);
    }

    #[Test]
    public function it_returns_correct_customer_display_name()
    {
        // Test with custom customer name
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'customer_name' => 'John Doe',
        ]);
        $this->assertEquals('John Doe', $reservation->customer_display_name);

        // Test with user name fallback
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'customer_name' => null,
        ]);
        $this->assertEquals($this->user->name, $reservation->customer_display_name);
    }

    #[Test]
    public function it_returns_correct_status_colors()
    {
        $statusColors = [
            'Pending' => 'warning',
            'Confirmed' => 'secondary',
            'Cancelled' => 'danger',
            'Completed' => 'dark',
        ];

        foreach ($statusColors as $status => $expectedColor) {
            $reservation = Reservation::factory()->create(['status' => $status]);
            $this->assertEquals($expectedColor, $reservation->status_color);
        }
    }

    #[Test]
    public function it_determines_if_reservation_can_be_cancelled()
    {
        // Future confirmed reservation - can be cancelled
        $reservation = Reservation::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->addDays(2),
        ]);
        $this->assertTrue($reservation->canBeCancelled());

        // Past reservation - cannot be cancelled
        $reservation = Reservation::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->subDays(1),
        ]);
        $this->assertFalse($reservation->canBeCancelled());

        // Cancelled reservation - cannot be cancelled again
        $reservation = Reservation::factory()->create([
            'status' => 'Cancelled',
            'booking_date' => now()->addDays(2),
        ]);
        $this->assertFalse($reservation->canBeCancelled());

        // Reservation within 24 hours - cannot be cancelled
        $reservation = Reservation::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->addHours(12),
        ]);
        $this->assertFalse($reservation->canBeCancelled());
    }

    #[Test]
    public function it_determines_if_reservation_can_be_modified()
    {
        // Future confirmed reservation - can be modified
        $reservation = Reservation::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->addDays(2),
        ]);
        $this->assertTrue($reservation->canBeModified());

        // Past reservation - cannot be modified
        $reservation = Reservation::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->subDays(1),
        ]);
        $this->assertFalse($reservation->canBeModified());

        // Cancelled reservation - cannot be modified
        $reservation = Reservation::factory()->create([
            'status' => 'Cancelled',
            'booking_date' => now()->addDays(2),
        ]);
        $this->assertFalse($reservation->canBeModified());

        // Reservation within 24 hours - cannot be modified
        $reservation = Reservation::factory()->create([
            'status' => 'Confirmed',
            'booking_date' => now()->addHours(12),
        ]);
        $this->assertFalse($reservation->canBeModified());
    }

    #[Test]
    public function it_auto_confirms_reservation()
    {
        $reservation = Reservation::factory()->create([
            'status' => 'Pending',
            'confirmed_at' => null,
        ]);

        $reservation->autoConfirm();

        $this->assertEquals('Confirmed', $reservation->status);
        $this->assertNotNull($reservation->confirmed_at);
    }

    #[Test]
    public function it_cancels_reservation()
    {
        $reservation = Reservation::factory()->create([
            'status' => 'Confirmed',
            'cancelled_at' => null,
        ]);

        $reservation->cancel();

        $this->assertEquals('Cancelled', $reservation->status);
        $this->assertNotNull($reservation->cancelled_at);
    }

    #[Test]
    public function it_detects_conflicts_with_existing_reservations()
    {
        // Create existing reservation
        $existingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => '2024-06-25',
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test overlapping reservation
        $newReservation = new Reservation([
            'field_id' => $this->field->id,
            'booking_date' => '2024-06-25',
            'start_time' => '11:00',
            'end_time' => '13:00',
        ]);

        $this->assertTrue($newReservation->hasConflict());

        // Test non-overlapping reservation
        $newReservation = new Reservation([
            'field_id' => $this->field->id,
            'booking_date' => '2024-06-25',
            'start_time' => '13:00',
            'end_time' => '15:00',
        ]);

        $this->assertFalse($newReservation->hasConflict());
    }

    #[Test]
    public function it_calculates_duration_in_hours()
    {
        $reservation = Reservation::factory()->create([
            'start_time' => '10:00',
            'end_time' => '13:00',
        ]);

        $this->assertEquals(3, $reservation->getDurationInHours());
    }

    #[Test]
    public function it_checks_if_within_working_hours()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);

        $this->assertTrue($reservation->isWithinWorkingHours());

        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'start_time' => '23:00',
            'end_time' => '01:00',
        ]);

        $this->assertFalse($reservation->isWithinWorkingHours());
    }

    #[Test]
    public function it_validates_duration_for_field()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'duration_hours' => 3,
        ]);

        $this->assertTrue($reservation->hasValidDuration());

        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'duration_hours' => 10, // Exceeds max
        ]);

        $this->assertFalse($reservation->hasValidDuration());
    }

    #[Test]
    public function scopes_work_correctly()
    {
        // Set a fixed time for consistent testing (12:00 PM)
        $this->travelTo(now()->setTime(12, 0));

        // Create test reservations
        $upcomingReservation = Reservation::factory()->create([
            'booking_date' => now()->addDays(1),
            'status' => 'Confirmed',
        ]);

        $pastReservation = Reservation::factory()->create([
            'booking_date' => now()->subDays(1),
            'status' => 'Confirmed',
        ]);

        // Create a reservation for today that's guaranteed to be in the future (2 PM)
        $todayFutureReservation = Reservation::factory()->create([
            'booking_date' => now(),
            'start_time' => '14:00',
            'status' => 'Confirmed',
        ]);

        // Create a reservation for today that's guaranteed to be in the past (10 AM)
        $todayPastReservation = Reservation::factory()->create([
            'booking_date' => now(),
            'start_time' => '10:00',
            'status' => 'Confirmed',
        ]);

        $cancelledReservation = Reservation::factory()->create([
            'booking_date' => now()->addDays(1),
            'status' => 'Cancelled',
        ]);

        // Test upcoming scope
        $upcoming = Reservation::upcoming()->get();
        $this->assertTrue($upcoming->contains($upcomingReservation));
        $this->assertTrue($upcoming->contains($todayFutureReservation));
        $this->assertFalse($upcoming->contains($todayPastReservation));
        $this->assertFalse($upcoming->contains($pastReservation));

        // Test today scope
        $today = Reservation::today()->get();
        $this->assertTrue($today->contains($todayFutureReservation));
        $this->assertTrue($today->contains($todayPastReservation));
        $this->assertFalse($today->contains($upcomingReservation));
        $this->assertFalse($today->contains($pastReservation));

        // Test active scope
        $active = Reservation::active()->get();
        $this->assertTrue($active->contains($upcomingReservation));
        $this->assertTrue($active->contains($pastReservation));
        $this->assertTrue($active->contains($todayFutureReservation));
        $this->assertTrue($active->contains($todayPastReservation));
        $this->assertFalse($active->contains($cancelledReservation));

        // Test forUser scope
        $userReservations = Reservation::forUser($this->user->id)->get();
        $this->assertCount(0, $userReservations); // None created for this user in this test
    }

    #[Test]
    public function factory_today_method_creates_reservation_for_today()
    {
        // Create a reservation using the factory's today() method
        $reservation = Reservation::factory()->today()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        // Verify the reservation was created for today
        $this->assertEquals(now()->toDateString(), $reservation->booking_date->format('Y-m-d'));

        // Verify the today scope can find this reservation
        $todayReservations = Reservation::today()->get();
        $this->assertTrue($todayReservations->contains($reservation));
        $this->assertCount(1, $todayReservations);
    }

    #[Test]
    public function today_scope_only_returns_todays_reservations()
    {
        // Create reservations for different dates
        $todayReservation = Reservation::factory()->today()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        $tomorrowReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->addDay(),
        ]);

        $yesterdayReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->subDay(),
        ]);

        // Test the today scope
        $todayReservations = Reservation::today()->get();

        $this->assertCount(1, $todayReservations);
        $this->assertTrue($todayReservations->contains($todayReservation));
        $this->assertFalse($todayReservations->contains($tomorrowReservation));
        $this->assertFalse($todayReservations->contains($yesterdayReservation));
    }

    #[Test]
    public function multiple_today_reservations_are_all_found()
    {
        // Create multiple reservations for today
        $reservation1 = Reservation::factory()->today()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'start_time' => '09:00',
            'end_time' => '10:00',
        ]);

        $reservation2 = Reservation::factory()->today()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'start_time' => '14:00',
            'end_time' => '15:00',
        ]);

        // Test the today scope finds both
        $todayReservations = Reservation::today()->get();

        $this->assertCount(2, $todayReservations);
        $this->assertTrue($todayReservations->contains($reservation1));
        $this->assertTrue($todayReservations->contains($reservation2));
    }

    #[Test]
    public function reservation_has_utilities_relationship()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        $utility = \App\Models\Utility::factory()->create([
            'name' => 'Test Utility',
            'hourly_rate' => 25.00,
            'is_active' => true,
        ]);

        // Test the relationship exists
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\BelongsToMany::class, $reservation->utilities());
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $reservation->utilities);
    }

    #[Test]
    public function can_attach_utility_to_reservation_with_pivot_data()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        $utility = \App\Models\Utility::factory()->create([
            'name' => 'Test Utility',
            'hourly_rate' => 25.00,
            'is_active' => true,
        ]);

        // Attach utility with pivot data
        $reservation->utilities()->attach($utility->id, [
            'hours' => 3,
            'rate' => 25.00,
            'cost' => 75.00,
        ]);

        // Verify the relationship
        $this->assertTrue($reservation->utilities->contains($utility));
        $this->assertEquals(1, $reservation->utilities->count());

        // Verify pivot data
        $pivotData = $reservation->utilities->first()->pivot;
        $this->assertEquals(3, $pivotData->hours);
        $this->assertEquals(25.00, $pivotData->rate);
        $this->assertEquals(75.00, $pivotData->cost);
    }

    #[Test]
    public function can_attach_multiple_utilities_to_reservation()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        $utility1 = \App\Models\Utility::factory()->create(['name' => 'Utility 1', 'hourly_rate' => 25.00]);
        $utility2 = \App\Models\Utility::factory()->create(['name' => 'Utility 2', 'hourly_rate' => 30.00]);

        // Attach multiple utilities
        $reservation->utilities()->attach([
            $utility1->id => ['hours' => 2, 'rate' => 25.00, 'cost' => 50.00],
            $utility2->id => ['hours' => 1, 'rate' => 30.00, 'cost' => 30.00],
        ]);

        // Verify both utilities are attached
        $this->assertEquals(2, $reservation->utilities->count());
        $this->assertTrue($reservation->utilities->contains($utility1));
        $this->assertTrue($reservation->utilities->contains($utility2));

        // Verify individual pivot data
        $utility1Pivot = $reservation->utilities->where('id', $utility1->id)->first()->pivot;
        $utility2Pivot = $reservation->utilities->where('id', $utility2->id)->first()->pivot;

        $this->assertEquals(2, $utility1Pivot->hours);
        $this->assertEquals(50.00, $utility1Pivot->cost);
        $this->assertEquals(1, $utility2Pivot->hours);
        $this->assertEquals(30.00, $utility2Pivot->cost);
    }

    #[Test]
    public function can_detach_utility_from_reservation()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        $utility = \App\Models\Utility::factory()->create();

        // Attach then detach
        $reservation->utilities()->attach($utility->id, [
            'hours' => 2,
            'rate' => 25.00,
            'cost' => 50.00,
        ]);

        $this->assertEquals(1, $reservation->utilities->count());

        $reservation->utilities()->detach($utility->id);

        $reservation->refresh();
        $this->assertEquals(0, $reservation->utilities->count());
    }

    #[Test]
    public function can_sync_utilities_on_reservation()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        $utility1 = \App\Models\Utility::factory()->create();
        $utility2 = \App\Models\Utility::factory()->create();
        $utility3 = \App\Models\Utility::factory()->create();

        // Initially attach utility1 and utility2
        $reservation->utilities()->attach([
            $utility1->id => ['hours' => 1, 'rate' => 25.00, 'cost' => 25.00],
            $utility2->id => ['hours' => 2, 'rate' => 30.00, 'cost' => 60.00],
        ]);

        $this->assertEquals(2, $reservation->utilities->count());

        // Sync to replace with utility2 and utility3
        $reservation->utilities()->sync([
            $utility2->id => ['hours' => 3, 'rate' => 30.00, 'cost' => 90.00],
            $utility3->id => ['hours' => 1, 'rate' => 35.00, 'cost' => 35.00],
        ]);

        $reservation->refresh();
        $this->assertEquals(2, $reservation->utilities->count());
        $this->assertFalse($reservation->utilities->contains($utility1));
        $this->assertTrue($reservation->utilities->contains($utility2));
        $this->assertTrue($reservation->utilities->contains($utility3));

        // Verify updated pivot data for utility2
        $utility2Pivot = $reservation->utilities->where('id', $utility2->id)->first()->pivot;
        $this->assertEquals(3, $utility2Pivot->hours);
        $this->assertEquals(90.00, $utility2Pivot->cost);
    }

    #[Test]
    public function pivot_table_has_timestamps()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        $utility = \App\Models\Utility::factory()->create();

        $reservation->utilities()->attach($utility->id, [
            'hours' => 2,
            'rate' => 25.00,
            'cost' => 50.00,
        ]);

        $pivotData = $reservation->utilities->first()->pivot;
        $this->assertNotNull($pivotData->created_at);
        $this->assertNotNull($pivotData->updated_at);
    }

    #[Test]
    public function reservation_has_correct_fillable_attributes()
    {
        $reservation = new Reservation;
        $fillable = $reservation->getFillable();

        $expectedFillable = [
            'field_id',
            'user_id',
            'booking_date',
            'start_time',
            'end_time',
            'duration_hours',
            'total_cost',
            'status',
            'customer_name',
            'customer_email',
            'customer_phone',
            'special_requests',
            'confirmed_at',
            'cancelled_at',
        ];

        foreach ($expectedFillable as $attribute) {
            $this->assertContains($attribute, $fillable);
        }
    }

    #[Test]
    public function reservation_has_correct_casts()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => '2024-06-25',
            'total_cost' => '150.50',
            'confirmed_at' => '2024-06-25 10:00:00',
            'cancelled_at' => null,
        ]);

        // Test date casting
        $this->assertInstanceOf(\Carbon\Carbon::class, $reservation->booking_date);

        // Test decimal casting (Laravel decimal cast returns string, not float)
        $this->assertIsString($reservation->total_cost);
        $this->assertEquals('150.50', $reservation->total_cost);

        // Test datetime casting
        $this->assertInstanceOf(\Carbon\Carbon::class, $reservation->confirmed_at);

        // Test null datetime
        $this->assertNull($reservation->cancelled_at);
    }

    #[Test]
    public function customer_display_name_returns_user_name_when_no_customer_name()
    {
        $user = User::factory()->create(['name' => 'John Doe']);
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $user->id,
            'customer_name' => null,
        ]);

        $this->assertEquals('John Doe', $reservation->customer_display_name);
    }

    #[Test]
    public function customer_display_name_handles_edge_cases()
    {
        // Test when customer_name is provided (should take precedence)
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'customer_name' => 'Custom Customer Name',
        ]);

        $this->assertEquals('Custom Customer Name', $reservation->customer_display_name);

        // Test when customer_name is empty string (should fall back to user name)
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'customer_name' => '',
        ]);

        $this->assertEquals($this->user->name, $reservation->customer_display_name);
    }

    #[Test]
    public function status_color_returns_correct_colors()
    {
        $testCases = [
            'Pending' => 'warning',
            'Confirmed' => 'secondary',
            'Cancelled' => 'danger',
            'Completed' => 'dark',
        ];

        foreach ($testCases as $status => $expectedColor) {
            $reservation = Reservation::factory()->create([
                'field_id' => $this->field->id,
                'user_id' => $this->user->id,
                'status' => $status,
            ]);

            $this->assertEquals($expectedColor, $reservation->status_color);
        }

        // Test default case with an invalid status (manually set after creation)
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
        ]);

        // Manually change status to test default case
        $reservation->status = 'Unknown';
        $this->assertEquals('secondary', $reservation->status_color);
    }

    #[Test]
    public function can_be_cancelled_returns_false_for_past_bookings()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->subDays(1),
            'status' => 'Confirmed',
        ]);

        $this->assertFalse($reservation->canBeCancelled());
    }

    #[Test]
    public function can_be_cancelled_returns_false_for_cancelled_status()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->addDays(2),
            'status' => 'Cancelled',
        ]);

        $this->assertFalse($reservation->canBeCancelled());
    }

    #[Test]
    public function can_be_cancelled_returns_false_within_24_hours()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->addHours(12), // Less than 24 hours
            'status' => 'Confirmed',
        ]);

        $this->assertFalse($reservation->canBeCancelled());
    }

    #[Test]
    public function can_be_modified_follows_same_rules_as_can_be_cancelled()
    {
        // Test future booking more than 24 hours away
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->addDays(2),
            'status' => 'Confirmed',
        ]);

        $this->assertTrue($reservation->canBeModified());

        // Test within 24 hours
        $reservation->update(['booking_date' => now()->addHours(12)]);
        $this->assertFalse($reservation->canBeModified());
    }

    #[Test]
    public function auto_confirm_updates_status_and_timestamp()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Pending',
            'confirmed_at' => null,
        ]);

        $reservation->autoConfirm();

        $this->assertEquals('Confirmed', $reservation->status);
        $this->assertNotNull($reservation->confirmed_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $reservation->confirmed_at);
    }

    #[Test]
    public function cancel_updates_status_and_timestamp()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
            'cancelled_at' => null,
        ]);

        $reservation->cancel();

        $this->assertEquals('Cancelled', $reservation->status);
        $this->assertNotNull($reservation->cancelled_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $reservation->cancelled_at);
    }

    #[Test]
    public function upcoming_scope_includes_future_reservations_and_todays_future_times()
    {
        // Set a fixed time for consistent testing (12:00 PM)
        $this->travelTo(now()->setTime(12, 0));

        // Create reservations for different dates
        $pastReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->subDays(1),
        ]);

        // Create a reservation for today that's guaranteed to be in the past (10 AM)
        $todayPastReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '10:00',
        ]);

        // Create a reservation for today that's guaranteed to be in the future (2 PM)
        $todayFutureReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now(),
            'start_time' => '14:00',
        ]);

        $futureReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'booking_date' => now()->addDays(1),
        ]);

        $upcomingReservations = Reservation::upcoming()->get();

        $this->assertFalse($upcomingReservations->contains($pastReservation));
        $this->assertFalse($upcomingReservations->contains($todayPastReservation));
        $this->assertTrue($upcomingReservations->contains($todayFutureReservation));
        $this->assertTrue($upcomingReservations->contains($futureReservation));
    }

    #[Test]
    public function for_user_scope_filters_by_user_id()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $reservation1 = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $user1->id,
        ]);

        $reservation2 = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $user2->id,
        ]);

        $user1Reservations = Reservation::forUser($user1->id)->get();
        $user2Reservations = Reservation::forUser($user2->id)->get();

        $this->assertTrue($user1Reservations->contains($reservation1));
        $this->assertFalse($user1Reservations->contains($reservation2));
        $this->assertTrue($user2Reservations->contains($reservation2));
        $this->assertFalse($user2Reservations->contains($reservation1));
    }

    #[Test]
    public function active_scope_excludes_cancelled_reservations()
    {
        $confirmedReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Confirmed',
        ]);

        $pendingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Pending',
        ]);

        $cancelledReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'status' => 'Cancelled',
        ]);

        $activeReservations = Reservation::active()->get();

        $this->assertTrue($activeReservations->contains($confirmedReservation));
        $this->assertTrue($activeReservations->contains($pendingReservation));
        $this->assertFalse($activeReservations->contains($cancelledReservation));
    }

    #[Test]
    public function has_conflict_excludes_own_id_when_provided()
    {
        $existingReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => '2024-06-25',
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test updating the same reservation (should not conflict with itself)
        $existingReservation->start_time = '10:30';
        $existingReservation->end_time = '12:30';

        $this->assertFalse($existingReservation->hasConflict($existingReservation->id));
    }

    #[Test]
    public function has_conflict_ignores_cancelled_reservations()
    {
        $cancelledReservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'booking_date' => '2024-06-25',
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Cancelled',
        ]);

        $newReservation = new Reservation([
            'field_id' => $this->field->id,
            'booking_date' => '2024-06-25',
            'start_time' => '11:00',
            'end_time' => '13:00',
        ]);

        // Should not conflict with cancelled reservation
        $this->assertFalse($newReservation->hasConflict());
    }

    #[Test]
    public function get_duration_in_hours_handles_different_time_formats()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'start_time' => '09:30',
            'end_time' => '12:45',
        ]);

        // 09:30 to 12:45 is 3 hours and 15 minutes = 3.25 hours, rounded to 3.3
        $this->assertEquals(3.3, $reservation->getDurationInHours());
    }

    #[Test]
    public function time_range_accessor_formats_correctly()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'start_time' => '09:30',
            'end_time' => '12:45',
        ]);

        $this->assertEquals('09:30 - 12:45', $reservation->time_range);
    }

    #[Test]
    public function has_valid_duration_delegates_to_field()
    {
        // Create field with specific duration constraints
        $field = Field::factory()->create([
            'min_booking_hours' => 2,
            'max_booking_hours' => 6,
        ]);

        $reservation = Reservation::factory()->create([
            'field_id' => $field->id,
            'user_id' => $this->user->id,
            'duration_hours' => 3,
        ]);

        // This test assumes the Field model has isValidDuration method
        // The actual implementation depends on the Field model
        $this->assertTrue($reservation->hasValidDuration());
    }

    #[Test]
    public function is_within_working_hours_delegates_to_field()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
            'start_time' => '10:00',
            'end_time' => '12:00',
        ]);

        // This test assumes the Field model has isWithinWorkingHours method
        // The actual implementation depends on the Field model
        $this->assertTrue($reservation->isWithinWorkingHours());
    }

    #[Test]
    public function mass_assignment_protection_works()
    {
        $reservation = new Reservation;

        // Test that non-fillable attributes are not mass assignable
        $reservation->fill([
            'id' => 999,
            'created_at' => now(),
            'updated_at' => now(),
            'field_id' => $this->field->id,
            'user_id' => $this->user->id,
        ]);

        // Only fillable attributes should be set
        $this->assertNull($reservation->id);
        $this->assertNull($reservation->created_at);
        $this->assertNull($reservation->updated_at);
        $this->assertEquals($this->field->id, $reservation->field_id);
        $this->assertEquals($this->user->id, $reservation->user_id);
    }
}
