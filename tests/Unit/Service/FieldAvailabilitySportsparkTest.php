<?php

namespace Tests\Unit\Service;

use App\Models\Field;
use App\Models\Reservation;
use App\Services\FieldAvailabilityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(FieldAvailabilityService::class)]
class FieldAvailabilitySportsparkTest extends TestCase
{
    use RefreshDatabase;

    private FieldAvailabilityService $service;

    private Field $soccerField;

    private Field $multiField;

    private Field $bolasField;

    private Field $patioField;

    private Field $sportsparkField;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new FieldAvailabilityService;

        // Create all the FPMP fields as they exist in the seeder
        $this->soccerField = Field::factory()->create([
            'name' => 'Veld Futbol',
            'type' => 'Soccer',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $this->multiField = Field::factory()->create([
            'name' => 'Veld Multi',
            'type' => 'Multi-Purpose',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $this->bolasField = Field::factory()->create([
            'name' => 'Veld Bolas',
            'type' => 'Bolas',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $this->patioField = Field::factory()->create([
            'name' => 'Patio Area',
            'type' => 'Patio',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        $this->sportsparkField = Field::factory()->create([
            'name' => 'Complete Sportpark',
            'type' => 'Sportspark',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);
    }

    #[Test]
    public function individual_fields_are_available_when_no_sportspark_reservation_exists()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // All individual fields should be available
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->multiField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->bolasField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->patioField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->sportsparkField, $date, $startTime, $endTime));
    }

    #[Test]
    public function individual_fields_are_blocked_when_sportspark_is_reserved()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // Create a Sportspark reservation
        $sportsparkReservation = Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'status' => 'Confirmed',
        ]);

        // Individual fields should NOT be available during Sportspark reservation
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, $startTime, $endTime));
        $this->assertFalse($this->service->isFieldAvailable($this->multiField, $date, $startTime, $endTime));
        $this->assertFalse($this->service->isFieldAvailable($this->bolasField, $date, $startTime, $endTime));
        $this->assertFalse($this->service->isFieldAvailable($this->patioField, $date, $startTime, $endTime));

        // Sportspark itself should NOT be available without excluding the reservation
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, $startTime, $endTime));

        // But should be available when excluding its own reservation (for editing purposes)
        $this->assertTrue($this->service->isFieldAvailable($this->sportsparkField, $date, $startTime, $endTime, $sportsparkReservation->id));
    }

    #[Test]
    public function sportspark_is_blocked_when_any_individual_field_is_reserved()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // Create a reservation on the soccer field
        Reservation::factory()->create([
            'field_id' => $this->soccerField->id,
            'booking_date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'status' => 'Confirmed',
        ]);

        // Sportspark should NOT be available when any individual field is reserved
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, $startTime, $endTime));

        // Other individual fields should still be available
        $this->assertTrue($this->service->isFieldAvailable($this->multiField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->bolasField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->patioField, $date, $startTime, $endTime));
    }

    #[Test]
    public function partial_overlap_with_sportspark_blocks_individual_fields()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create a Sportspark reservation from 10:00-14:00
        Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '14:00',
            'status' => 'Confirmed',
        ]);

        // Test various overlapping scenarios - all should be blocked
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, '09:00', '11:00')); // Overlaps start
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, '13:00', '15:00')); // Overlaps end
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, '11:00', '13:00')); // Contained within
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, '09:00', '15:00')); // Contains sportspark time

        // Non-overlapping times should be available
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, '08:00', '10:00')); // Before
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, '14:00', '16:00')); // After
    }

    #[Test]
    public function partial_overlap_with_individual_field_blocks_sportspark()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create a soccer field reservation from 10:00-12:00
        Reservation::factory()->create([
            'field_id' => $this->soccerField->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Test various overlapping scenarios for Sportspark - all should be blocked
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, '09:00', '11:00')); // Overlaps start
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, '11:00', '13:00')); // Overlaps end
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, '10:30', '11:30')); // Contained within
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, '09:00', '13:00')); // Contains individual field time

        // Non-overlapping times should be available
        $this->assertTrue($this->service->isFieldAvailable($this->sportsparkField, $date, '08:00', '10:00')); // Before
        $this->assertTrue($this->service->isFieldAvailable($this->sportsparkField, $date, '12:00', '14:00')); // After
    }

    #[Test]
    public function multiple_individual_field_reservations_block_sportspark()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create reservations on multiple individual fields
        Reservation::factory()->create([
            'field_id' => $this->soccerField->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        Reservation::factory()->create([
            'field_id' => $this->multiField->id,
            'booking_date' => $date,
            'start_time' => '14:00',
            'end_time' => '16:00',
            'status' => 'Confirmed',
        ]);

        // Sportspark should be blocked during both time periods
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, '10:00', '12:00'));
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, '14:00', '16:00'));

        // Sportspark should also be blocked for times that span multiple reservations
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, '09:00', '17:00'));

        // But should be available between the reservations
        $this->assertTrue($this->service->isFieldAvailable($this->sportsparkField, $date, '12:00', '14:00'));
    }

    #[Test]
    public function cancelled_reservations_do_not_block_availability()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // Create a cancelled Sportspark reservation
        Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'status' => 'Cancelled',
        ]);

        // Individual fields should be available since the Sportspark reservation is cancelled
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->multiField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->bolasField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->patioField, $date, $startTime, $endTime));
    }

    #[Test]
    public function exclude_reservation_id_works_with_sportspark_logic()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // Create a Sportspark reservation
        $sportsparkReservation = Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'status' => 'Confirmed',
        ]);

        // Without excluding the reservation, individual fields should be blocked
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, $startTime, $endTime));

        // When excluding the Sportspark reservation, individual fields should be available
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, $startTime, $endTime, $sportsparkReservation->id));
    }

    #[Test]
    public function exclude_reservation_id_works_for_individual_field_blocking_sportspark()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // Create an individual field reservation
        $soccerReservation = Reservation::factory()->create([
            'field_id' => $this->soccerField->id,
            'booking_date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'status' => 'Confirmed',
        ]);

        // Without excluding the reservation, Sportspark should be blocked
        $this->assertFalse($this->service->isFieldAvailable($this->sportsparkField, $date, $startTime, $endTime));

        // When excluding the individual field reservation, Sportspark should be available
        $this->assertTrue($this->service->isFieldAvailable($this->sportsparkField, $date, $startTime, $endTime, $soccerReservation->id));
    }

    #[Test]
    public function different_dates_do_not_interfere_with_sportspark_logic()
    {
        $date1 = now()->addDays(1)->format('Y-m-d');
        $date2 = now()->addDays(2)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // Create a Sportspark reservation on date1
        Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date1,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'status' => 'Confirmed',
        ]);

        // Individual fields should be blocked on date1
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date1, $startTime, $endTime));

        // But should be available on date2
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date2, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->sportsparkField, $date2, $startTime, $endTime));
    }

    #[Test]
    public function pending_reservations_do_not_block_sportspark_logic()
    {
        $date = now()->addDays(1)->format('Y-m-d');
        $startTime = '10:00';
        $endTime = '12:00';

        // Create a pending Sportspark reservation (not confirmed)
        Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'status' => 'Pending',
        ]);

        // Individual fields should be available since the Sportspark reservation is not confirmed
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->multiField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->bolasField, $date, $startTime, $endTime));
        $this->assertTrue($this->service->isFieldAvailable($this->patioField, $date, $startTime, $endTime));
    }

    #[Test]
    public function sportspark_logic_works_with_boundary_times()
    {
        $date = now()->addDays(1)->format('Y-m-d');

        // Create a Sportspark reservation from 10:00-12:00
        Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
        ]);

        // Adjacent times should be available (no overlap)
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, '08:00', '10:00')); // Ends when Sportspark starts
        $this->assertTrue($this->service->isFieldAvailable($this->soccerField, $date, '12:00', '14:00')); // Starts when Sportspark ends

        // Overlapping times should be blocked
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, '09:30', '10:30')); // Overlaps start
        $this->assertFalse($this->service->isFieldAvailable($this->soccerField, $date, '11:30', '12:30')); // Overlaps end
    }
}
