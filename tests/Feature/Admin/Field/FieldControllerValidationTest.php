<?php

namespace Tests\Feature\Admin;

use App\Http\Controllers\Admin\FieldController;
use App\Models\Amenity;
use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(FieldController::class)]
class FieldControllerValidationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;

    protected function setUp(): void
    {
        parent::setUp();
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    #[Test]
    public function store_validates_required_fields()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), []);

        $response->assertRedirect();
        $response->assertSessionHasErrors([
            'name',
            'type',
            'hourly_rate',
            'capacity',
            'status',
        ]);
    }

    #[Test]
    public function store_validates_name_max_length()
    {
        $longName = str_repeat('a', 256); // Exceeds 255 character limit

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => $longName,
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['name']);
    }

    #[Test]
    public function store_validates_description_max_length()
    {
        $longDescription = str_repeat('a', 1001); // Exceeds 1000 character limit

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'description' => $longDescription,
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['description']);
    }

    #[Test]
    public function store_validates_hourly_rate_numeric_and_range()
    {
        // Test non-numeric value
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 'not-a-number',
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['hourly_rate']);

        // Test negative value
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => -10.00,
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['hourly_rate']);

        // Test value exceeding maximum
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 10000.00,
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['hourly_rate']);
    }

    #[Test]
    public function store_validates_night_hourly_rate_when_provided()
    {
        // Test negative value
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'night_hourly_rate' => -10.00,
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['night_hourly_rate']);

        // Test value exceeding maximum
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'night_hourly_rate' => 10000.00,
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['night_hourly_rate']);
    }

    #[Test]
    public function store_validates_night_time_start_format()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'night_time_start' => 'invalid-time-format',
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['night_time_start']);
    }

    #[Test]
    public function store_validates_capacity_integer_and_range()
    {
        // Test non-integer value
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 'not-a-number',
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['capacity']);

        // Test value below minimum
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 0,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['capacity']);

        // Test value exceeding maximum
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 1001,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['capacity']);
    }

    #[Test]
    public function store_validates_type_is_valid_field_type()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'InvalidFieldType',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['type']);
    }

    #[Test]
    public function store_validates_status_is_valid_status()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'InvalidStatus',
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['status']);
    }

    #[Test]
    public function store_validates_amenities_exist_in_database()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
                'amenities' => [999999], // Non-existent amenity ID
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['amenities.0']);
    }

    #[Test]
    public function store_validates_utilities_exist_in_database()
    {
        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
                'utilities' => [999999], // Non-existent utility ID
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['utilities.0']);
    }

    #[Test]
    public function store_accepts_valid_field_types()
    {
        $validTypes = ['Sportspark', 'Patio', 'Office', 'Multi-Purpose', 'Soccer', 'Bolas'];

        foreach ($validTypes as $type) {
            $response = $this->actingAs($this->admin)
                ->post(route('admin.fields.store'), [
                    'name' => "Test {$type} Field",
                    'type' => $type,
                    'hourly_rate' => 50.00,
                    'capacity' => 20,
                    'status' => 'Active',
                    'min_booking_hours' => 1,
                    'max_booking_hours' => 8,
                ]);

            $response->assertRedirect(route('admin.fields.index'));
            $response->assertSessionHas('success');
        }
    }

    #[Test]
    public function store_accepts_valid_statuses()
    {
        $validStatuses = ['Active', 'Inactive']; // 'Under Maintenance' is commented out in current getStatuses()

        foreach ($validStatuses as $status) {
            $response = $this->actingAs($this->admin)
                ->post(route('admin.fields.store'), [
                    'name' => "Test Field {$status}",
                    'type' => 'Soccer',
                    'hourly_rate' => 50.00,
                    'capacity' => 20,
                    'status' => $status,
                    'min_booking_hours' => 1,
                    'max_booking_hours' => 8,
                ]);

            $response->assertRedirect(route('admin.fields.index'));
            $response->assertSessionHas('success');
        }
    }

    #[Test]
    public function store_accepts_valid_time_formats()
    {
        $validTimes = ['18:00', '19:30', '20:15', '21:45'];

        foreach ($validTimes as $time) {
            $response = $this->actingAs($this->admin)
                ->post(route('admin.fields.store'), [
                    'name' => "Test Field {$time}",
                    'type' => 'Soccer',
                    'hourly_rate' => 50.00,
                    'night_time_start' => $time,
                    'capacity' => 20,
                    'status' => 'Active',
                    'min_booking_hours' => 1,
                    'max_booking_hours' => 8,
                ]);

            $response->assertRedirect(route('admin.fields.index'));
            $response->assertSessionHas('success');
        }
    }

    #[Test]
    public function store_accepts_valid_amenities_and_utilities()
    {
        $amenities = Amenity::factory()->count(3)->create();
        $utilities = Utility::factory()->count(2)->create();

        $response = $this->actingAs($this->admin)
            ->post(route('admin.fields.store'), [
                'name' => 'Test Field with Valid Relationships',
                'type' => 'Soccer',
                'hourly_rate' => 50.00,
                'capacity' => 20,
                'status' => 'Active',
                'amenities' => $amenities->pluck('id')->toArray(),
                'utilities' => $utilities->pluck('id')->toArray(),
                'min_booking_hours' => 1,
                'max_booking_hours' => 8,
            ]);

        $response->assertRedirect(route('admin.fields.index'));
        $response->assertSessionHas('success');

        // Verify relationships were created
        $field = Field::where('name', 'Test Field with Valid Relationships')->first();
        $this->assertCount(3, $field->amenities);
        $this->assertCount(2, $field->utilities);
    }
}
