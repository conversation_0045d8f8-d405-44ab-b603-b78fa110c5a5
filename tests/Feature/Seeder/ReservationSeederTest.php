<?php

namespace Tests\Feature\Seeders;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Database\Seeders\ExistingReservationsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ExistingReservationsSeeder::class)]
class ReservationSeederTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed the required dependencies
        $this->artisan('db:seed', ['--class' => 'UserRoleSeeder']);
        $this->artisan('db:seed', ['--class' => 'AmenitySeeder']);
        $this->artisan('db:seed', ['--class' => 'UtilitySeeder']);
        $this->artisan('db:seed', ['--class' => 'FPMPFieldSeeder']);
    }

    #[Test]
    public function existing_reservations_seeder_creates_reservations_successfully()
    {
        // Ensure we start with no reservations
        $this->assertEquals(0, Reservation::count());

        // Run the seeder
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);

        // Verify reservations were created
        $this->assertGreaterThan(0, Reservation::count());

        // Verify we have reservations with utilities
        $reservationsWithUtilities = Reservation::has('utilities')->count();
        $this->assertGreaterThan(0, $reservationsWithUtilities);
    }

    #[Test]
    public function seeder_handles_decimal_duration_hours()
    {
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);

        // Check for reservations with half-hour durations
        $halfHourReservations = Reservation::whereRaw('duration_hours - FLOOR(duration_hours) = 0.5')->count();
        $this->assertGreaterThan(0, $halfHourReservations);
    }

    #[Test]
    public function seeder_handles_admin_booked_reservations()
    {
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);

        // Check for reservations with booked_by field set
        $adminBookedReservations = Reservation::whereNotNull('booked_by')->count();
        $this->assertGreaterThan(0, $adminBookedReservations);

        // Verify the booked_by user exists
        $adminBookedReservation = Reservation::whereNotNull('booked_by')->first();
        $bookedByUser = User::find($adminBookedReservation->booked_by);
        $this->assertNotNull($bookedByUser);
    }

    #[Test]
    public function seeder_attaches_utilities_with_correct_pivot_data()
    {
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);

        $reservationWithUtilities = Reservation::has('utilities')->first();
        $this->assertNotNull($reservationWithUtilities);

        $utility = $reservationWithUtilities->utilities->first();
        $this->assertNotNull($utility->pivot->hours);
        $this->assertNotNull($utility->pivot->rate);
        $this->assertNotNull($utility->pivot->cost);
        $this->assertGreaterThan(0, $utility->pivot->hours);
        $this->assertGreaterThan(0, $utility->pivot->rate);
        $this->assertGreaterThan(0, $utility->pivot->cost);
    }

    #[Test]
    public function seeder_adjusts_dates_correctly()
    {
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);

        // All reservations should have booking dates from today onwards
        $pastReservations = Reservation::where('booking_date', '<', now()->format('Y-m-d'))->count();
        $this->assertEquals(0, $pastReservations);

        // Should have reservations starting from today
        $todayOrFutureReservations = Reservation::where('booking_date', '>=', now()->format('Y-m-d'))->count();
        $this->assertGreaterThan(0, $todayOrFutureReservations);
    }

    #[Test]
    public function seeder_prevents_duplicate_reservations()
    {
        // Run the seeder twice
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);
        $firstRunCount = Reservation::count();

        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);
        $secondRunCount = Reservation::count();

        // Second run should clear and recreate, so count should be the same
        $this->assertEquals($firstRunCount, $secondRunCount);
    }

    #[Test]
    public function export_command_generates_valid_seeder()
    {
        // First create some reservations
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder']);

        $originalCount = Reservation::count();
        $this->assertGreaterThan(0, $originalCount);

        // Export to a test seeder
        $testSeederFile = 'TestExportSeeder.php';
        Artisan::call('reservations:export-seeder', ['--file' => $testSeederFile]);

        // Verify the file was created
        $seederPath = database_path("seeders/{$testSeederFile}");
        $this->assertFileExists($seederPath);

        // Verify the file contains expected content
        $content = file_get_contents($seederPath);
        $this->assertStringContainsString('booked_by_email', $content);
        $this->assertStringContainsString('duration_hours', $content);
        $this->assertStringContainsString('utilities', $content);

        // Clean up
        unlink($seederPath);
    }
}
