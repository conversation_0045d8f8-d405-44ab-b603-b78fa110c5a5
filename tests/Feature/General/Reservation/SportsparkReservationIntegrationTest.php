<?php

namespace Tests\Feature\Reservation;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SportsparkReservationIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Field $soccerField;

    private Field $sportsparkField;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        $this->soccerField = Field::factory()->create([
            'name' => 'Soccer Field',
            'type' => 'Soccer',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 1.0,
            'max_booking_hours' => 8.0,
        ]);

        $this->sportsparkField = Field::factory()->create([
            'name' => 'Complete Sportpark',
            'type' => 'Sportspark',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'min_booking_hours' => 4.0,
            'max_booking_hours' => 10.0,
        ]);
    }

    #[Test]
    public function check_availability_endpoint_respects_sportspark_reservations()
    {
        $this->actingAs($this->user);

        $date = now()->addDays(1)->format('Y-m-d');

        // Create a Sportspark reservation
        Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '14:00',
            'status' => 'Confirmed',
            'user_id' => $this->user->id,
        ]);

        // Check availability for soccer field during Sportspark time
        $response = $this->postJson(route('reservations.check-availability'), [
            'field_id' => $this->soccerField->id,
            'date' => $date,
            'duration_hours' => 2.0,
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        // Should have no available slots during Sportspark time (10:00-14:00)
        $conflictingSlots = array_filter($data['slots'], function ($slot) {
            $startTime = $slot['start_time'];

            return $startTime >= '10:00' && $startTime < '14:00';
        });

        $this->assertEmpty($conflictingSlots, 'No slots should be available during Sportspark reservation time');

        // Should have available slots before and after
        $beforeSlots = array_filter($data['slots'], function ($slot) {
            return $slot['start_time'] < '10:00';
        });
        $afterSlots = array_filter($data['slots'], function ($slot) {
            return $slot['start_time'] >= '14:00';
        });

        $this->assertNotEmpty($beforeSlots, 'Should have slots available before Sportspark time');
        $this->assertNotEmpty($afterSlots, 'Should have slots available after Sportspark time');
    }

    #[Test]
    public function check_availability_endpoint_blocks_sportspark_when_individual_field_reserved()
    {
        $this->actingAs($this->user);

        $date = now()->addDays(1)->format('Y-m-d');

        // Create a soccer field reservation
        Reservation::factory()->create([
            'field_id' => $this->soccerField->id,
            'booking_date' => $date,
            'start_time' => '10:00',
            'end_time' => '12:00',
            'status' => 'Confirmed',
            'user_id' => $this->user->id,
        ]);

        // Check availability for Sportspark field
        $response = $this->postJson(route('reservations.check-availability'), [
            'field_id' => $this->sportsparkField->id,
            'date' => $date,
            'duration_hours' => 4.0,
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        // Should have no available slots that would overlap with soccer field reservation
        $conflictingSlots = array_filter($data['slots'], function ($slot) {
            $startTime = $slot['start_time'];
            $endTime = $slot['end_time'];

            // Check if this slot would overlap with 10:00-12:00 soccer reservation
            return $startTime < '12:00' && $endTime > '10:00';
        });

        $this->assertEmpty($conflictingSlots, 'No Sportspark slots should overlap with individual field reservation');
    }

    #[Test]
    public function available_end_times_endpoint_respects_sportspark_logic()
    {
        $this->actingAs($this->user);

        $date = now()->addDays(1)->format('Y-m-d');

        // Create a Sportspark reservation from 12:00-16:00
        Reservation::factory()->create([
            'field_id' => $this->sportsparkField->id,
            'booking_date' => $date,
            'start_time' => '12:00',
            'end_time' => '16:00',
            'status' => 'Confirmed',
            'user_id' => $this->user->id,
        ]);

        // Check available end times for soccer field starting at 10:00
        $response = $this->postJson(route('reservations.available-end-times'), [
            'field_id' => $this->soccerField->id,
            'date' => $date,
            'start_time' => '10:00',
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['success']);
        $endTimeValues = array_column($data['end_times'], 'value');

        // Should not include end times that would overlap with Sportspark (12:00-16:00)
        $this->assertContains('12:00', $endTimeValues, 'Should be able to end exactly when Sportspark starts');
        $this->assertNotContains('12:30', $endTimeValues, 'Should not be able to end during Sportspark time');
        $this->assertNotContains('13:00', $endTimeValues, 'Should not be able to end during Sportspark time');
        $this->assertNotContains('15:30', $endTimeValues, 'Should not be able to end during Sportspark time');
    }

    #[Test]
    public function sportspark_availability_is_blocked_by_any_individual_field_via_endpoint()
    {
        $this->actingAs($this->user);

        $date = now()->addDays(1)->format('Y-m-d');

        // Create multiple individual field reservations at different times
        $multiField = Field::factory()->create([
            'type' => 'Multi-Purpose',
            'status' => 'Active',
            'opening_time' => '08:00',
            'closing_time' => '22:00',
        ]);

        Reservation::factory()->create([
            'field_id' => $this->soccerField->id,
            'booking_date' => $date,
            'start_time' => '09:00',
            'end_time' => '10:00',
            'status' => 'Confirmed',
            'user_id' => $this->user->id,
        ]);

        Reservation::factory()->create([
            'field_id' => $multiField->id,
            'booking_date' => $date,
            'start_time' => '15:00',
            'end_time' => '16:00',
            'status' => 'Confirmed',
            'user_id' => $this->user->id,
        ]);

        // Check Sportspark availability
        $response = $this->postJson(route('reservations.check-availability'), [
            'field_id' => $this->sportsparkField->id,
            'date' => $date,
            'duration_hours' => 4.0,
        ]);

        $response->assertStatus(200);
        $data = $response->json();

        // Should have no slots that would conflict with either individual reservation
        foreach ($data['slots'] as $slot) {
            $startTime = $slot['start_time'];
            $endTime = $slot['end_time'];

            // Should not overlap with 09:00-10:00 soccer reservation
            $this->assertFalse(
                ($startTime < '10:00' && $endTime > '09:00'),
                "Sportspark slot {$startTime}-{$endTime} should not overlap with soccer reservation 09:00-10:00"
            );

            // Should not overlap with 15:00-16:00 multi field reservation
            $this->assertFalse(
                ($startTime < '16:00' && $endTime > '15:00'),
                "Sportspark slot {$startTime}-{$endTime} should not overlap with multi field reservation 15:00-16:00"
            );
        }
    }
}
