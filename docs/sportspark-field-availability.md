# Sportspark Field Availability Logic

## Overview

The "Complete Sportpark" field type has special availability logic that ensures when it's booked, it reserves the entire facility, preventing conflicts with individual field reservations.

## Implementation Details

### Core Logic in `FieldAvailabilityService::isFieldAvailable()`

The method now handles two scenarios:

#### 1. When checking Sportspark field availability:
- **Blocks Sportspark** if ANY individual field (Soccer, Multi-Purpose, Bolas, Patio) has a confirmed reservation during the requested time
- **Allows Sportspark** only when all individual fields are free during the requested time period

#### 2. When checking individual field availability:
- **Blocks individual fields** if ANY Sportspark field has a confirmed reservation during the requested time
- **Allows individual fields** only when no Sportspark reservations exist during the requested time period

### Key Features

1. **Bidirectional Blocking**: 
   - Sportspark reservations block all individual fields
   - Individual field reservations block Sportspark

2. **Overlap Detection**: Uses proper time overlap logic (`start_time < end_time AND end_time > start_time`)

3. **Status Filtering**: Only considers "Confirmed" reservations (ignores Pending, Cancelled)

4. **Exclude Logic**: Supports excluding specific reservations (for editing scenarios)

5. **Date Isolation**: Reservations on different dates don't interfere with each other

## Usage Examples

### Scenario 1: Booking Individual Fields
```php
// If Sportspark is reserved 10:00-14:00 on 2024-01-15
$available = $service->isFieldAvailable($soccerField, '2024-01-15', '11:00', '13:00');
// Returns: false (blocked by Sportspark)

$available = $service->isFieldAvailable($soccerField, '2024-01-15', '14:00', '16:00');
// Returns: true (after Sportspark time)
```

### Scenario 2: Booking Sportspark
```php
// If Soccer field is reserved 10:00-12:00 on 2024-01-15
$available = $service->isFieldAvailable($sportsparkField, '2024-01-15', '09:00', '13:00');
// Returns: false (would overlap with Soccer reservation)

$available = $service->isFieldAvailable($sportsparkField, '2024-01-15', '12:00', '16:00');
// Returns: true (starts after Soccer reservation ends)
```

### Scenario 3: Editing Reservations
```php
// When editing a Sportspark reservation (ID: 123)
$available = $service->isFieldAvailable($sportsparkField, '2024-01-15', '10:00', '14:00', 123);
// Returns: true (excludes the reservation being edited)
```

## Field Types Affected

- **Sportspark**: "Complete Sportpark" (type: 'Sportspark')
- **Individual Fields**: All non-Sportspark types (Soccer, Multi-Purpose, Bolas, Patio, etc.)

## API Integration

The logic is automatically applied to all reservation endpoints:

- `POST /reservations/check-availability`
- `POST /reservations/available-end-times`
- Any other endpoint using `FieldAvailabilityService`

## Testing

Comprehensive test coverage includes:

### Unit Tests (`FieldAvailabilitySportsparkTest`)
- Basic blocking scenarios
- Partial overlap detection
- Multiple field conflicts
- Exclude reservation logic
- Boundary time conditions
- Status filtering (Confirmed vs Pending/Cancelled)
- Date isolation

### Integration Tests (`SportsparkReservationIntegrationTest`)
- End-to-end API testing
- Real reservation scenarios
- Multiple field interactions

## Database Considerations

The implementation uses efficient queries with:
- `whereHas()` for field type filtering
- Proper date and time overlap conditions
- Status filtering for confirmed reservations only
- Optional exclusion of specific reservation IDs

## Performance Notes

- Queries are optimized to check field types via relationships
- Uses database-level filtering to minimize data transfer
- Leverages existing indexes on reservation fields (field_id, booking_date, status)

## Future Enhancements

Potential improvements could include:
- Caching of field type mappings
- Bulk availability checking for multiple fields
- Advanced conflict resolution strategies
- Integration with field maintenance schedules
