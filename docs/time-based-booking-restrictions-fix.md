# Time-Based Booking Restrictions Fix

## Issue Summary

Two specific test methods in the ReservationControllerTest were failing:

1. `store_validates_five_minute_booking_restriction` - ✅ **Already passing**
2. `update_validates_sixty_minute_advance_booking_restriction` - ❌ **Was failing**

## Root Cause Analysis

The failing test `update_validates_sixty_minute_advance_booking_restriction` was expecting a specific error message about booking at least 1 hour before the reservation start time, but it was getting a different error about working hours instead.

### The Problem

The test was trying to book 30 minutes from the current time (`now()->addMinutes(30)`), but if the test ran outside working hours (before 08:00 or after 22:00), the booking time would be outside the field's working hours (08:00-22:00), causing the working hours validation to fail before the 60-minute advance booking validation could be checked.

### Validation Order in Controller

The ReservationController validates in this order:
1. **Working hours validation** (lines 508-514 in update method)
2. **60-minute advance booking validation** (lines 530-535 in update method)

If the working hours validation fails first, the 60-minute validation never gets executed.

## Solution

Updated the failing test to ensure the booking time is always within working hours, similar to how the passing `store_validates_five_minute_booking_restriction` test handles this scenario.

### Before (Failing)
```php
// Try to update to a time that's less than 60 minutes from now
$futureTime = now()->addMinutes(30); // 30 minutes from now
$updateData = [
    'field_id' => $this->field->id,
    'booking_date' => $futureTime->format('Y-m-d'),
    'start_time' => $futureTime->format('H:i'),
    'end_time' => $futureTime->copy()->addHour()->format('H:i'),
];
```

### After (Fixed)
```php
// Try to update to a time that's less than 60 minutes from now, but ensure it's within working hours
$now = now();

// If current time is outside working hours (8:00-22:00), use a time within working hours
if ($now->hour < 8) {
    // If before 8 AM, set to 10:30 AM today (30 minutes from 10:00)
    $futureTime = $now->copy()->setTime(10, 30);
    // Set "now" to 10:00 AM for the test context
    $this->travelTo($now->copy()->setTime(10, 0));
} elseif ($now->hour >= 22) {
    // If after 10 PM, set to 10:30 AM tomorrow (30 minutes from 10:00)
    $futureTime = $now->copy()->addDay()->setTime(10, 30);
    // Set "now" to 10:00 AM tomorrow for the test context
    $this->travelTo($now->copy()->addDay()->setTime(10, 0));
} else {
    // Current time is within working hours, just add 30 minutes
    $futureTime = $now->copy()->addMinutes(30);
}

$updateData = [
    'field_id' => $this->field->id,
    'booking_date' => $futureTime->format('Y-m-d'),
    'start_time' => $futureTime->format('H:i'),
    'end_time' => $futureTime->copy()->addHour()->format('H:i'),
];
```

## Business Logic Validation

The fix ensures that the time-based booking restrictions work correctly:

### 60-Minute Advance Booking Rule
- **Store (Create)**: Cannot create reservations less than 60 minutes before start time
- **Update (Modify)**: Cannot update reservations to start less than 60 minutes from current time
- **Implementation**: Uses `now()->diffInMinutes($reservationDateTime, false) < 60` validation

### Working Hours Validation
- **Field Hours**: Default 08:00 - 22:00 (configurable per field)
- **Priority**: Working hours validation happens before time restriction validation
- **Implementation**: Uses `Field::isWithinWorkingHours()` method

### 24-Hour Modification Rule
- **Existing Reservations**: Can only be modified if original reservation is more than 24 hours away
- **Implementation**: Uses `Reservation::canBeModified()` method
- **Note**: This checks the original reservation time, not the new time being set

## Test Results

After the fix:
- ✅ `store_validates_five_minute_booking_restriction` - **Passing**
- ✅ `store_validates_five_minute_booking_restriction_with_json` - **Passing**  
- ✅ `update_validates_sixty_minute_advance_booking_restriction` - **Passing**
- ✅ All 78 ReservationControllerTest tests - **Passing**

## Key Learnings

1. **Test Environment Considerations**: Tests that involve time calculations need to account for when they might run (day/night, weekends, etc.)

2. **Validation Order Matters**: Understanding the order of validations in the controller helps predict which error message will be returned

3. **Time Travel in Tests**: Using `$this->travelTo()` is essential for time-based tests to ensure consistent behavior regardless of when the test runs

4. **Working Hours Impact**: Time-based validations must consider field working hours to avoid false negatives

## Files Modified

- `tests/Feature/General/Reservation/ReservationControllerTest.php` - Fixed the failing test method

## Files Reviewed (No Changes Needed)

- `app/Http/Controllers/ReservationController.php` - Validation logic is correct
- `app/Models/Reservation.php` - Business logic methods are working properly
- `app/Services/FieldAvailabilityService.php` - Field availability logic is functioning correctly
